<template>
	<view class="box">
		<u-navbar :is-back="true" leftIconColor="#000000" :autoBack="true" :bgColor="'transparent'">
			<view slot="left">
				<img src="/static/wback.png" style="width: 50rpx;height: 50rpx;"></img>
			</view>
		</u-navbar>
		<view style="position: relative;">
			<!--轮播-->
			<view class="swiper-container swiper-mask">
				<swiper :circular="true" :autoplay="true" :interval="3000" :duration="500" :indicator-dots="false"
					style="width: 100%; height: 400rpx;">
					<swiper-item v-for="(item, index) in info.images" :key="index">
						<image :src="item" style="width: 100%; height: 100%; border-radius: 12rpx;" mode="aspectFill">
						</image>
					</swiper-item>
				</swiper>
			</view>
		</view>
		<view style="padding: 30rpx;">
			<view style="display: flex;align-items: center;">
				<view>
					<image style="width: 120rpx;height: 120rpx;border: 4rpx #ffffff solid;border-radius: 23rpx;"
						:src="info.logo">
					</image>
				</view>
				<view style="padding-left:20rpx;">
					<view style="font-size: 36rpx;color: #FFFFFF;font-weight: 600;">{{ info.name }}</view>
					<view style="font-size: 28rpx;color: #FFFFFF;font-weight: 400;margin-top: 25rpx;">
						<text>举办过{{ info.activity_number }}场活动</text>
						<text style="margin: 0rpx 30rpx;">•</text>
						<text>{{ info.activity_people_number }}人参与过</text>
					</view>
				</view>
			</view>
			<view
				style="background-color: rgba(255,255,255,0.2);min-height: 325rpx;padding: 30rpx;border-radius: 24rpx;position: relative;margin-top: 40rpx;">
				<image class="noImg" src="https://naweigetetest2.hschool.com.cn/dyqc/club_t.png" style="width: 240rpx;"
					mode="widthFix"></image>
				<image class="noImg" src="https://naweigetetest2.hschool.com.cn/dyqc/club_r.png"
					style="width: 85rpx;position: absolute;right: 30rpx;top: -20rpx;" mode="widthFix"></image>
				<view>
					<view class="third-center1" ref="richTextContainer" v-if="!showToggleButtons" style="height: auto;">
						<view class="v_html">
							<rich-text style="white-space: pre-line;" :nodes="info.content"></rich-text>
						</view>
					</view>
					<view class="third-center" v-if="showToggleButtons"
						:style="{ height: richTextShow ? 'auto' : '95px', overflow: 'hidden', margin: '0 auto', paddingBottom: '0' }"
						ref="richTextContainer">
						<view class="v_html">
							<rich-text style="white-space: pre-line;" :nodes="info.content"></rich-text>
						</view>
					</view>
					<view style="height: 1px;width: 100%;background-color: #FFFFFF;opacity: 0.2;margin: 30rpx 0rpx;">
					</view>
					<view>
						<view v-if="!richTextShow" @click="richTextShow = true"
							style="color: #9996AB;font-size: 30rpx;text-align: center;">
							<view style="vertical-align: middle;display: inline-block;margin-right: 20rpx;">展开全部</view>
							<image src="/static/xia.png" style="width: 30rpx;height: 30rpx;vertical-align: middle;">
							</image>
						</view>
						<view v-if="richTextShow" @click="richTextShow = false"
							style="color: #9996AB;font-size: 30rpx;text-align: center;">
							<view style="vertical-align: middle;display: inline-block;margin-right: 20rpx;">收起介绍</view>
							<image src="/static/shang.png" style="width: 30rpx;height: 30rpx;vertical-align: middle;">
							</image>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="jxz">
			<view style="background-color: #F5F5F5;min-height: 100vh;">
				<view class="hot flex flex-column">
					<view class="content flex align-items flex-column">
						<view class="flex flex-column w-100 bbb" v-for="(item, index) in hotList" :key="index"
							@click="detail(item.id)">
							<view class="flex" style="margin-top: 30rpx">
								<view class="right flex flex-column" style="align-items: self-start">
									<span class="title white-space" style="wi">{{ item.title }}</span>
									<view class="first-image flex align-items" style="margin-top: 20rpx;width: 100%;">
										<image :src="item.user.avatar"
											style="width: 36rpx;height: 36rpx;border-radius: 80rpx;"></image>
									
										<span class="white-space"
											style="color: #9C9C9C;margin-left: 10rpx;width: 210rpx;">{{
												item.user.nickname }}</span>
										<!-- <span v-else class="white-space"
											style="color: #9C9C9C;margin-left: 10rpx;width: 210rpx;">{{
												item.teacher.name }}</span> -->
										<view class="white-space" style="width: 56%;">
											<span style="color:#0CA013;margin-left: 10rpx;"
												v-for="items_t in item.classes_cate">#{{ items_t
												}}</span>
										</view>
									</view>

									<view class="flex flex-column" style="margin-top: 20rpx;">
										<view class="time flex align-items white-space">
											<span class="time_tex" v-if="item.status == 2">{{ '报名中' }}</span>
											<!-- <span class="time_tex time_texs" v-if="item.status == 4">{{ '进行中' }}</span> -->
											<span style="margin:0 20rpx;">{{ dateWeeks(item.start_time) }}</span><span
												style="width: 1rpx;height: 22rpx;background: #323232;"></span>
											<span class="white-space" style="margin-left: 20rpx;width: 260rpx;">{{
												item.address }}{{
													item.address_detail }}</span>
										</view>

									</view>

									<view class="flex align-items justify-center"
										style="margin-top: 20rpx;margin-bottom: 32rpx;">
										<view class="imgs_con_div" v-for="(items_img, index) in item.images.slice(0, 3)"
											:key="index">
											<image class="imgs_con" :src="items_img" mode="aspectFill"></image>
										</view>
									</view>
								</view>
							</view>

							<view class="bottom flex align-items">
								<view class="flex align-items toptext">
									<span class="flex align-items">
										<u-avatar-group :urls="item.join_info.users" keyName="avatar" size="30"
											:maxCount="4" gap="0.4"></u-avatar-group>
										<image src="/static/index/dian.png"
											:class="item.join_info.users.length > 0 ? '' : 'smalld'"
											style="width: 60rpx;height: 60rpx;margin-left:-20rpx;z-index: 1;"></image>
									</span>
									<view class="number flex align-items">
										{{ Number(item.join_info.people_number) + "人已上车" }}
									</view>
								</view>
								<span v-if="item.status == 5" class="part1 flex justify-center align-items">
									已结束
								</span>
								<span v-if="item.status == 2" class="part flex justify-center align-items">
									上车
								</span>

							</view>


						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import {
	dateWeek
} from '../../utils/dateFormat'
export default {
	data() {
		return {
			id: 0,//机构ID
			user_id: 0,//发布人的ID
			hotList: [],
			richTextShow: false,
			showToggleButtons: false, // 控制是否显示展开/收起按钮
			// 轮播图数据
			info: {},
			page: 1,
			hotList: [],
		}
	},
	onLoad(op) {
		this.id = 3;
		this.user_id = 2806;
		this.getInfo();
		this.getList();
	},
	mounted() {
		this.checkContentHeight();
	},
	updated() {
		this.checkContentHeight();
	},
	methods: {
		getList() {
			uni.$u.http.get(`/api/school.new_activity/activity_list?user_id=${this.user_id}&page=${this.page}&limit=10&order=normal&status=1,2,3,4,5,-1`).then(res => {
				this.hotList = res.data.list.data;
			}).catch(error => {
				uni.showToast({
					title: '请求失败，请稍后再试',
					icon: 'none',
					duration: 2000
				});
			});
		},
		getInfo() {
			uni.$u.http.get('/api/school/shop/detail?id=' + this.id).then(res => {
				this.info = res.data.detail;
			}).catch(error => {
				uni.showToast({
					title: '请求失败，请稍后再试',
					icon: 'none',
					duration: 2000
				});
			});
		},
		dateWeeks(e) {
			return dateWeek(e);
		},
		checkContentHeight() {
			// 使用 uni.createSelectorQuery 获取富文本容器的高度
			const query = uni.createSelectorQuery().in(this);
			query.select('.v_html').boundingClientRect(data => {
				if (data && data.height > 95) { // 300px 是容器的固定高度
					this.showToggleButtons = true;
				} else {
					this.showToggleButtons = false;
				}
			}).exec();
		},
	}
}
</script>

<style scoped>
page {}

.box {
	background-color: rgb(16, 8, 58);
	min-height: 100vh;
	background-image: url("https://naweigetetest2.hschool.com.cn/dyqc/club_bg.png");
	background-size: 100%;
	background-repeat: no-repeat;
	font-family: PingFang SC, PingFang SC;
}

.jxz {
	background-image: url("https://naweigetetest2.hschool.com.cn/dyqc/club_d.png");
	background-size: 100% auto;
	background-repeat: no-repeat;
	background-position: center top;
	min-height: 100vh;
	padding-top: 148px;
}

.swiper-container {
	width: 100%;
	margin-bottom: 20rpx;
}

/* 使用mask-image实现底部虚化效果 */
.swiper-mask {
	-webkit-mask-image: linear-gradient(to bottom,
			rgba(0, 0, 0, 1) 0%,
			rgba(0, 0, 0, 1) 60%,
			rgba(0, 0, 0, 0.7) 75%,
			rgba(0, 0, 0, 0.3) 90%,
			rgba(0, 0, 0, 0) 100%);
	mask-image: linear-gradient(to bottom,
			rgba(0, 0, 0, 1) 0%,
			rgba(0, 0, 0, 1) 60%,
			rgba(0, 0, 0, 0.7) 75%,
			rgba(0, 0, 0, 0.3) 90%,
			rgba(0, 0, 0, 0) 100%);
}

.v_html {
	font-size: 30rpx;
	color: #FFFFFF;
	line-height: 45rpx;
}
.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.justify-center {
		justify-content: center;
	}

	.space-between {
		justify-content: space-between;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.mar-top-30 {
		margin-top: 30rpx;
	}
</style>
